import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import 'react-native-gesture-handler';

import AppNavigator from './src/navigation/AppNavigator';
import { WeatherProvider } from './src/context/WeatherContext';
import { AuthProvider } from './src/context/AuthContext';
import TensorFlowService from './src/services/TensorFlowService';

export default function App() {
	// Initialize TensorFlow model on app startup
	useEffect(() => {
		const initializeTensorFlow = async () => {
			try {
				console.log('Initializing TensorFlow Lite model...');
				const result = await TensorFlowService.initializeModel();
				if (result.success) {
					console.log('TensorFlow model initialized successfully');
				} else {
					console.warn('TensorFlow model initialization failed:', result.error);
				}
			} catch (error) {
				console.error('Error initializing TensorFlow model:', error);
			}
		};

		initializeTensorFlow();
	}, []);

	return (
		<SafeAreaProvider>
			<AuthProvider>
				<WeatherProvider>
					<NavigationContainer>
						<StatusBar style='dark' />
						<AppNavigator />
					</NavigationContainer>
				</WeatherProvider>
			</AuthProvider>
		</SafeAreaProvider>
	);
}

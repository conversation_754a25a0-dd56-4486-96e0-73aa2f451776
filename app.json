{"expo": {"name": "Plant Disease Detection", "slug": "plant-ai-disease-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.plantai.diseaseapp"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#325721"}, "package": "com.plantai.diseaseapp", "permissions": ["android.permission.CAMERA", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.RECORD_AUDIO"]}, "web": {"favicon": "./assets/favicon.png"}, "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"], "plugins": ["expo-camera", "expo-location", "expo-image-picker", "expo-dev-client"], "extra": {"eas": {"projectId": "23334d8f-24fd-4de7-ac9d-8f4a8f27ec5b"}}}}
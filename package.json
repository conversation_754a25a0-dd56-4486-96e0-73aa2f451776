{"name": "PlantAIDiseaseApp", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "npx expo start", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web"}, "dependencies": {"@bam.tech/react-native-image-resizer": "^3.0.11", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@supabase/supabase-js": "^2.50.5", "axios": "^1.6.0", "expo": "~53.0.0", "expo-camera": "~16.1.10", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-location": "~18.1.6", "expo-media-library": "~17.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.5", "react-native-fast-tflite": "^1.6.1", "react-native-gesture-handler": "~2.24.0", "react-native-modal": "^13.0.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.0.0", "react-native-web": "^0.20.0", "expo-dev-client": "~5.2.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}
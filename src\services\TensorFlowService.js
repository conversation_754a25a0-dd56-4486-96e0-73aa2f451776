import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';

// Conditional imports to handle Expo Go compatibility
let TensorflowModel = null;
let ImageResizer = null;

try {
	// Try to import native modules - will fail in Expo Go
	TensorflowModel = require('react-native-fast-tflite').TensorflowModel;
	ImageResizer = require('@bam.tech/react-native-image-resizer').default;
} catch (error) {
	console.warn(
		'Native modules not available (likely running in Expo Go):',
		error.message,
	);
}

class TensorFlowService {
	static model = null;
	static isModelLoaded = false;
	static modelPath = null;

	// Disease classes for apple model (adjust based on your model's output)
	static diseaseClasses = [
		'Apple___Apple_scab',
		'Apple___Black_rot',
		'Apple___Cedar_apple_rust',
		'Apple___healthy',
	];

	// Confidence threshold for disease detection
	static CONFIDENCE_THRESHOLD = 0.7;

	/**
	 * Initialize and load the TensorFlow Lite model
	 */
	static async initializeModel() {
		try {
			// Check if native modules are available
			if (!TensorflowModel) {
				console.warn(
					'TensorFlow Lite not available - running in Expo Go or native modules not linked',
				);
				return {
					success: false,
					error:
						'TensorFlow Lite not available in current environment. Use Expo Development Build for native module support.',
				};
			}

			if (this.isModelLoaded && this.model) {
				return { success: true, message: 'Model already loaded' };
			}

			console.log('Loading TensorFlow Lite model...');

			// Load the model asset
			const modelAsset = Asset.fromModule(
				require('../../assets/apple_model_final.tflite'),
			);
			await modelAsset.downloadAsync();

			// Copy model to a local file system location
			const modelUri = modelAsset.localUri || modelAsset.uri;
			const localModelPath = `${FileSystem.documentDirectory}apple_model_final.tflite`;

			// Copy the model file to the document directory
			await FileSystem.copyAsync({
				from: modelUri,
				to: localModelPath,
			});

			// Load the model
			this.model = await TensorflowModel.create({
				model: localModelPath,
			});

			this.modelPath = localModelPath;
			this.isModelLoaded = true;

			console.log('TensorFlow Lite model loaded successfully');
			return { success: true, message: 'Model loaded successfully' };
		} catch (error) {
			console.error('Error loading TensorFlow Lite model:', error);
			return {
				success: false,
				error: `Failed to load model: ${error.message}`,
			};
		}
	}

	/**
	 * Preprocess image for model input
	 * @param {string} imageUri - URI of the image to preprocess
	 * @returns {Promise<Object>} Preprocessed image data
	 */
	static async preprocessImage(imageUri) {
		try {
			// Check if ImageResizer is available
			if (!ImageResizer) {
				console.warn('ImageResizer not available - running in Expo Go');
				return {
					success: false,
					error: 'Image processing not available in current environment.',
				};
			}

			// Resize image to model input size (typically 224x224 for most plant disease models)
			const resizedImage = await ImageResizer.createResizedImage(
				imageUri,
				224, // width
				224, // height
				'JPEG',
				80, // quality
				0, // rotation
				undefined, // outputPath
				false, // keepMeta
				{
					mode: 'cover',
					onlyScaleDown: false,
				},
			);

			return {
				success: true,
				imageUri: resizedImage.uri,
				width: resizedImage.width,
				height: resizedImage.height,
			};
		} catch (error) {
			console.error('Error preprocessing image:', error);
			return {
				success: false,
				error: `Failed to preprocess image: ${error.message}`,
			};
		}
	}

	/**
	 * Run inference on the preprocessed image
	 * @param {string} imageUri - URI of the preprocessed image
	 * @returns {Promise<Object>} Prediction results
	 */
	static async runInference(imageUri) {
		try {
			if (!this.isModelLoaded || !this.model) {
				const initResult = await this.initializeModel();
				if (!initResult.success) {
					return initResult;
				}
			}

			console.log('Running inference on image:', imageUri);

			// Run the model inference
			const outputs = await this.model.run({
				image: imageUri,
			});

			// Process the outputs (adjust based on your model's output format)
			const predictions = this.processModelOutputs(outputs);

			return {
				success: true,
				predictions,
				rawOutputs: outputs,
			};
		} catch (error) {
			console.error('Error running inference:', error);
			return {
				success: false,
				error: `Inference failed: ${error.message}`,
			};
		}
	}

	/**
	 * Process model outputs to extract meaningful predictions
	 * @param {Object} outputs - Raw model outputs
	 * @returns {Array} Processed predictions
	 */
	static processModelOutputs(outputs) {
		try {
			// Assuming the model outputs probabilities for each class
			// Adjust this based on your specific model's output format
			const probabilities = outputs[0] || outputs.output || outputs;

			if (!Array.isArray(probabilities)) {
				throw new Error('Invalid model output format');
			}

			const predictions = probabilities.map((probability, index) => ({
				className: this.diseaseClasses[index] || `Class_${index}`,
				confidence: probability,
				isDisease: !this.diseaseClasses[index]?.includes('healthy'),
			}));

			// Sort by confidence (highest first)
			predictions.sort((a, b) => b.confidence - a.confidence);

			// Filter predictions above threshold
			const significantPredictions = predictions.filter(
				(pred) => pred.confidence >= this.CONFIDENCE_THRESHOLD,
			);

			return significantPredictions.length > 0
				? significantPredictions
				: [predictions[0]];
		} catch (error) {
			console.error('Error processing model outputs:', error);
			return [];
		}
	}

	/**
	 * Analyze plant image for disease detection using TensorFlow Lite
	 * @param {string} imageUri - URI of the image to analyze
	 * @returns {Promise<Object>} Analysis results
	 */
	static async analyzeImage(imageUri) {
		try {
			console.log('Starting TensorFlow analysis for image:', imageUri);

			// Check if TensorFlow modules are available
			if (!TensorflowModel || !ImageResizer) {
				console.warn(
					'TensorFlow Lite or ImageResizer not available - falling back to simulation',
				);
				return {
					success: false,
					error:
						'TensorFlow Lite not available. Please use Expo Development Build or EAS Build for native module support.',
				};
			}

			// Step 1: Preprocess the image
			const preprocessResult = await this.preprocessImage(imageUri);
			if (!preprocessResult.success) {
				return preprocessResult;
			}

			// Step 2: Run inference
			const inferenceResult = await this.runInference(
				preprocessResult.imageUri,
			);
			if (!inferenceResult.success) {
				return inferenceResult;
			}

			// Step 3: Format results for the app
			const analysisResults = this.formatAnalysisResults(
				inferenceResult.predictions,
				imageUri,
			);

			return {
				success: true,
				data: analysisResults,
			};
		} catch (error) {
			console.error('Error in TensorFlow analysis:', error);
			return {
				success: false,
				error: `Analysis failed: ${error.message}`,
			};
		}
	}

	/**
	 * Format analysis results for the app
	 * @param {Array} predictions - Model predictions
	 * @param {string} originalImageUri - Original image URI
	 * @returns {Object} Formatted analysis results
	 */
	static formatAnalysisResults(predictions, originalImageUri) {
		const topPrediction = predictions[0];
		const isHealthy = topPrediction?.className?.includes('healthy') || false;

		// Extract plant type and disease from class name
		const plantType = this.extractPlantType(topPrediction?.className);
		const diseaseName = this.extractDiseaseName(topPrediction?.className);

		const diseases = isHealthy
			? []
			: predictions
					.filter((pred) => !pred.className.includes('healthy'))
					.map((pred) => ({
						id: pred.className.toLowerCase().replace(/[^a-z0-9]/g, '-'),
						name: this.extractDiseaseName(pred.className),
						confidence: pred.confidence,
						severity: this.determineSeverity(pred.confidence),
						description: this.getDiseaseDescription(pred.className),
						symptoms: this.getDiseaseSymptoms(pred.className),
						treatment: this.getDiseaseTreatment(pred.className),
						prevention: this.getDiseasePrevention(pred.className),
					}));

		return {
			imageUri: originalImageUri,
			plantType,
			diseases,
			healthStatus: isHealthy
				? 'Healthy'
				: diseases.length > 0
				? 'Diseased'
				: 'Unknown',
			confidence: topPrediction?.confidence || 0,
			analysisTimestamp: new Date().toISOString(),
			recommendations: this.generateRecommendations(diseases, isHealthy),
		};
	}

	/**
	 * Extract plant type from class name
	 */
	static extractPlantType(className) {
		if (!className) return 'Unknown';
		return className.split('___')[0] || 'Unknown';
	}

	/**
	 * Extract disease name from class name
	 */
	static extractDiseaseName(className) {
		if (!className) return 'Unknown';
		const parts = className.split('___');
		return parts[1]?.replace(/_/g, ' ') || 'Unknown Disease';
	}

	/**
	 * Determine disease severity based on confidence
	 */
	static determineSeverity(confidence) {
		if (confidence >= 0.9) return 'High';
		if (confidence >= 0.7) return 'Moderate';
		return 'Low';
	}

	/**
	 * Get disease description (placeholder - extend with real data)
	 */
	static getDiseaseDescription(className) {
		const diseaseDescriptions = {
			Apple___Apple_scab:
				'A fungal disease that causes dark, scabby lesions on apple leaves and fruits.',
			Apple___Black_rot:
				'A fungal disease causing black, circular lesions on apple fruits and leaves.',
			Apple___Cedar_apple_rust:
				'A fungal disease that creates orange spots on apple leaves.',
		};
		return (
			diseaseDescriptions[className] ||
			'Disease detected. Consult with agricultural expert for detailed information.'
		);
	}

	/**
	 * Get disease symptoms (placeholder - extend with real data)
	 */
	static getDiseaseSymptoms(className) {
		const diseaseSymptoms = {
			Apple___Apple_scab: [
				'Dark, scabby lesions on leaves',
				'Fruit deformation',
				'Premature leaf drop',
			],
			Apple___Black_rot: ['Black, circular lesions', 'Fruit rot', 'Leaf spots'],
			Apple___Cedar_apple_rust: [
				'Orange spots on leaves',
				'Reduced fruit quality',
				'Leaf yellowing',
			],
		};
		return (
			diseaseSymptoms[className] || [
				'Abnormal leaf coloration',
				'Unusual growth patterns',
				'Potential fruit damage',
			]
		);
	}

	/**
	 * Get disease treatment (placeholder - extend with real data)
	 */
	static getDiseaseTreatment(className) {
		const treatments = {
			Apple___Apple_scab: [
				'Apply fungicide spray',
				'Remove infected leaves',
				'Improve air circulation',
			],
			Apple___Black_rot: [
				'Prune infected branches',
				'Apply copper-based fungicide',
				'Remove mummified fruits',
			],
			Apple___Cedar_apple_rust: [
				'Remove nearby cedar trees',
				'Apply preventive fungicide',
				'Ensure proper drainage',
			],
		};
		return (
			treatments[className] || [
				'Consult agricultural expert',
				'Apply appropriate fungicide',
				'Improve plant care',
			]
		);
	}

	/**
	 * Get disease prevention (placeholder - extend with real data)
	 */
	static getDiseasePrevention(className) {
		return [
			'Plant disease-resistant varieties',
			'Ensure proper spacing for air circulation',
			'Regular monitoring and early detection',
			'Maintain proper soil drainage',
			'Follow integrated pest management practices',
		];
	}

	/**
	 * Generate recommendations based on analysis
	 */
	static generateRecommendations(diseases, isHealthy) {
		if (isHealthy) {
			return [
				'Your plant appears healthy! Continue with regular care.',
				'Monitor regularly for any changes in appearance.',
				'Maintain proper watering and fertilization schedule.',
			];
		}

		if (diseases.length === 0) {
			return [
				'Unable to identify specific disease. Consider consulting an expert.',
				'Monitor plant closely for symptom development.',
				'Ensure optimal growing conditions.',
			];
		}

		return [
			`Detected ${diseases[0].name} with ${(
				diseases[0].confidence * 100
			).toFixed(1)}% confidence.`,
			'Take immediate action to prevent spread.',
			'Consider consulting with agricultural extension services.',
		];
	}

	/**
	 * Cleanup resources
	 */
	static async cleanup() {
		try {
			if (this.model) {
				// Note: react-native-fast-tflite doesn't have explicit cleanup method
				// The model will be garbage collected
				this.model = null;
			}
			this.isModelLoaded = false;
			console.log('TensorFlow resources cleaned up');
		} catch (error) {
			console.error('Error during cleanup:', error);
		}
	}
}

export default TensorFlowService;
